{"contract/base/tests/test_vcloud_db.py::test_create_user_service": true, "contract/base/tests/test_vcloud_db.py::test_get_user_service": true, "contract/base/tests/test_vcloud_db.py::test_update_user_service": true, "contract/base/tests/test_vcloud_db.py::test_delete_user_service": true, "contract/base/tests/test_vcloud_db.py::test_count_user_services": true, "contract/base/tests/test_vcloud_db.py::test_batch_update_user_services": true, "contract/base/tests/test_vcloud_db.py::test_batch_upsert_user_services": true, "contract/base/tests/test_vcloud_db.py::test_query_user_services_backward_compatibility": true, "contract/base/tests/test_vcloud_db.py::test_query_user_services_advanced": true, "contract/base/tests/test_vcloud_db.py::test_query_edge_cases": true, "contract/base/tests/test_vcloud_db.py::test_data_validation": true, "contract/base/tests/test_vcloud_db.py::test_service_lifecycle": true, "contract/base/tests/test_vcloud_db.py::test_new_fields_storage_and_retrieval": true, "contract/base/tests/test_vcloud_db.py::test_batch_id_query": true, "contract/base/tests/test_vcloud_db.py::test_enhanced_address_status_query_with_pagination": true, "contract/base/tests/test_vcloud_db.py::test_ids_address_service_activated_query": true, "contract/base/tests/test_vcloud_db.py::test_ids_provider_address_query": true, "contract/base/tests/test_vcloud_db.py::test_query_edge_cases_with_new_fields": true, "contract/base/tests/test_vcloud_db.py::test_create_many_existing_service_detection": true, "contract/base/tests/test_vcloud_db.py::test_batch_create_user_services": true, "contract/base/tests/test_vcloud_db.py::test_create_many_basic_functionality": true, "contract/base/tests/test_vcloud_db.py::test_create_many_duplicate_detection": true, "contract/base/tests/test_vcloud_db.py::test_create_many_validation_errors": true, "contract/base/tests/test_vcloud_db.py::test_create_many_json_parsing_errors": true, "contract/base/tests/test_vcloud_db.py::test_create_many_vs_batch_create_compatibility": true, "contract/base/tests/test_vcloud_db.py::test_create_many_large_batch": true}