#[glue::contract]
mod vcloud_db {
    use serde::{Deserialize, Serialize};
    use std::collections::HashMap;

    // User Service data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct UserService {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(default)]
        pub duration: i64,
        #[serde(default)]
        pub amount: f64,
        #[serde(rename = "publicKey", default)]
        pub public_key: String,
        #[serde(default)]
        pub provider: String,
        #[serde(rename = "providerAddress", default)]
        pub provider_address: String,
        #[serde(default)]
        pub address: String,
        #[serde(rename = "serviceID", default)]
        pub service_id: String,
        #[serde(rename = "serviceActivated", default)]
        pub service_activated: bool,
        #[serde(default)]
        pub status: String,  // Changed from ServiceStatus enum to String
        #[serde(rename = "serviceOptions", default)]
        pub service_options: HashMap<String, String>,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        // New fields added
        #[serde(rename = "endAt", default)]
        pub end_at: i64,
        #[serde(rename = "serviceActivateTS", default)]
        pub service_activate_ts: i64,
        #[serde(rename = "serviceRunningTS", default)]
        pub service_running_ts: i64,
        #[serde(rename = "serviceAbortTS", default)]
        pub service_abort_ts: i64,
        #[serde(rename = "serviceDoneTS", default)]
        pub service_done_ts: i64,
        #[serde(rename = "serviceRefundTS", default)]
        pub service_refund_ts: i64,
        #[serde(default)]
        pub service: String,
        #[serde(rename = "createdAddr", default)]
        pub created_addr: String,
        #[serde(rename = "labelHash", default)]
        pub label_hash: String,
    }

    // Order data structures
    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct SingleServiceBriefInfo {
        #[serde(rename = "userServiceID", default)]
        pub user_service_id: String,
        #[serde(rename = "duration", default)]
        pub duration: i64,
        #[serde(rename = "amount", default)]
        pub amount: f64,
    }

    #[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
    pub struct Order {
        #[serde(rename = "_id")]
        pub _id: String,
        #[serde(rename = "createdAt", default)]
        pub created_at: i64,
        #[serde(rename = "updatedAt", default)]
        pub updated_at: i64,
        #[serde(rename = "deletedAt", default)]
        pub deleted_at: i64,
        #[serde(rename = "type", default)]
        pub order_type: String,
        #[serde(rename = "amount", default)]
        pub amount: f64,
        #[serde(rename = "amountPaid", default)]
        pub amount_paid: f64,
        #[serde(rename = "provider", default)]
        pub provider: String,
        #[serde(rename = "address", default)]
        pub address: String,
        #[serde(rename = "recipient", default)]
        pub recipient: String,
        #[serde(rename = "status", default)]
        pub status: String,
        #[serde(rename = "lastPaymentTS", default)]
        pub last_payment_ts: i64,
        #[serde(rename = "paidTS", default)]
        pub paid_ts: i64,
        #[serde(rename = "filedTS", default)]
        pub filed_ts: i64,
        #[serde(rename = "publicKey", default)]
        pub public_key: String,
        #[serde(rename = "userServiceIDs", default)]
        pub user_service_ids: Vec<String>,
        #[serde(rename = "items", default)]
        pub items: Vec<SingleServiceBriefInfo>,
    }

    /// Query parameters for filtering user services
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct UserServiceQueryParams {
        #[serde(rename = "serviceID")]
        pub service_id: Option<String>,
        pub address: Option<String>,
        pub provider: Option<String>,
        #[serde(rename = "providerAddress")]
        pub provider_address: Option<String>,  // New field for provider_address filtering
        pub status: Option<String>,  // Changed from ServiceStatus to String
        #[serde(rename = "serviceActivated")]
        pub service_activated: Option<bool>,  // New field for service_activated filtering
        pub ids: Option<Vec<String>>,  // New field for batch ID queries
        #[serde(rename = "createdAtStart")]
        pub created_at_start: Option<i64>,
        #[serde(rename = "createdAtEnd")]
        pub created_at_end: Option<i64>,
        #[serde(rename = "updatedAtStart")]
        pub updated_at_start: Option<i64>,
        #[serde(rename = "updatedAtEnd")]
        pub updated_at_end: Option<i64>,
        pub offset: Option<u64>,
        pub limit: Option<u64>,
        #[serde(rename = "sortBy")]
        pub sort_by: Option<String>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    /// Query parameters for filtering orders
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderQueryParam {
        #[serde(rename = "_ids", default)]
        pub ids: Vec<String>,
        #[serde(rename = "serviceID", default)]
        pub service_id: String,
        #[serde(rename = "service", default)]
        pub service: String,
        #[serde(rename = "address", default)]
        pub address: String,
        #[serde(rename = "recipient", default)]
        pub recipient: String,
        #[serde(rename = "type", default)]
        pub order_type: String,
        #[serde(rename = "statuses", default)]
        pub statuses: Vec<String>,
        #[serde(rename = "tsStart")]
        pub ts_start: Option<i64>,
        #[serde(rename = "tsEnd")]
        pub ts_end: Option<i64>,
        #[serde(rename = "limit")]
        pub limit: Option<u64>,
        #[serde(rename = "offset")]
        pub offset: Option<u64>,
        #[serde(rename = "sortDesc")]
        pub sort_desc: Option<bool>,
    }

    /// Distinct parameters for order queries
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderDistinctParam {
        #[serde(rename = "distinctField", default)]
        pub distinct_field: String,
        #[serde(rename = "address", default)]
        pub address: String,
        #[serde(rename = "statuses", default)]
        pub statuses: Vec<String>,
    }

    /// Batch operation result
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct BatchResult {
        pub created: u64,
        pub updated: u64,
        pub deleted: u64,
        pub errors: Vec<String>,
    }



    /// User service update parameters
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct UserServiceUpdate {
        pub filter: UserServiceQueryParams,
        pub update_data: serde_json::Value,
    }

    /// Order update parameters
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderUpdate {
        pub filter: OrderQueryParam,
        pub update_data: serde_json::Value,
    }

    /// User service bulk write operation
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct UserServiceBulkWriteOperation {
        #[serde(rename = "type")]
        pub operation_type: String, // "insert", "update", "delete_many"
        pub filter: Option<UserServiceQueryParams>,
        pub data: Option<serde_json::Value>,
    }

    /// Order bulk write operation
    #[derive(Debug, Serialize, Deserialize, Clone)]
    pub struct OrderBulkWriteOperation {
        #[serde(rename = "type")]
        pub operation_type: String, // "insert", "update", "delete_many"
        pub filter: Option<OrderQueryParam>,
        pub data: Option<serde_json::Value>,
    }

    #[glue::storage]
    pub struct VCloudDB {
        pub user_services: glue::collections::Map<String, UserService>,
        pub orders: glue::collections::Map<String, Order>,
    }

    impl VCloudDB {
        #[glue::bind_index]
        pub fn bind_index(&mut self) {
            // User Service indexes
            // Index by provider for efficient provider-based queries
            self.user_services.bind_index(
                "user_services_provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by address for efficient address-based queries
            self.user_services.bind_index(
                "user_services_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by provider_address for efficient provider_address-based queries
            self.user_services.bind_index(
                "user_services_provider_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider_address, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.user_services.bind_index(
                "user_services_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status, v.created_at)]
                }),
            );

            // Index by service_id for efficient service_id-based queries
            self.user_services.bind_index(
                "user_services_service_id",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_id, v.created_at)]
                }),
            );

            // Index by service_activated for efficient service_activated-based queries
            self.user_services.bind_index(
                "user_services_service_activated",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.service_activated, v.created_at)]
                }),
            );

            // Composite index: address + status for efficient combined queries
            self.user_services.bind_index(
                "user_services_address_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.status, v.created_at)]
                }),
            );

            // Composite index: provider + status for efficient combined queries
            self.user_services.bind_index(
                "user_services_provider_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.provider, v.status, v.created_at)]
                }),
            );

            // Composite index: address + service_activated for IDs + address + service_activated queries
            self.user_services.bind_index(
                "user_services_address_service_activated",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.service_activated, v.created_at)]
                }),
            );

            // Index by created_at for time-based queries
            self.user_services.bind_index(
                "user_services_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Index by updated_at for time-based queries
            self.user_services.bind_index(
                "user_services_updated_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.updated_at)]
                }),
            );

            // Order indexes
            // Index by address for efficient address-based queries
            self.orders.bind_index(
                "order_address",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.address, v.created_at)]
                }),
            );

            // Index by recipient for efficient recipient-based queries
            self.orders.bind_index(
                "order_recipient",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.recipient, v.created_at)]
                }),
            );

            // Index by provider for efficient provider-based queries
            self.orders.bind_index(
                "order_provider",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.provider, v.created_at)]
                }),
            );

            // Index by status for efficient status-based queries
            self.orders.bind_index(
                "order_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.status, v.created_at)]
                }),
            );

            // Index by order_type for efficient type-based queries
            self.orders.bind_index(
                "order_type",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{:0>19}", v.order_type, v.created_at)]
                }),
            );

            // Index by paid_ts for time-based queries
            self.orders.bind_index(
                "order_paid_ts",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.paid_ts)]
                }),
            );

            // Index by filed_ts for time-based queries
            self.orders.bind_index(
                "order_filed_ts",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.filed_ts)]
                }),
            );

            // Index by created_at for time-based queries
            self.orders.bind_index(
                "order_created_at",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{:0>19}", v.created_at)]
                }),
            );

            // Composite index: address + status for efficient combined queries
            self.orders.bind_index(
                "order_address_status",
                glue::collections::Index::new(|v| -> Vec<String> {
                    vec![format!("{}-{}-{:0>19}", v.address, v.status, v.created_at)]
                }),
            );
        }

        #[glue::constructor]
        pub fn new() -> Self {
            let mut ret = Self {
                user_services: glue::collections::Map::new(),
                orders: glue::collections::Map::new(),
            };
            ret.bind_index();
            ret
        }

        /// Get current timestamp - returns actual Unix timestamp
        fn get_current_timestamp(&self) -> i64 {
            // Get current Unix timestamp in seconds
            std::time::SystemTime::now()
                .duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default()
                .as_nanos() as i64
        }

        /// Apply timestamp handling logic based on input values
        fn apply_timestamp_handling(&self, service: &mut UserService) {
            let current_time = self.get_current_timestamp();

            // For created_at: if input is 0, set to current timestamp
            if service.created_at == 0 {
                service.created_at = current_time;
            }

            // For updated_at: if input is 0, set to current timestamp
            if service.updated_at == 0 {
                service.updated_at = current_time;
            }

            // For deleted_at: if input is 0, keep it as 0 (not deleted)
            // No action needed as 0 means not deleted
        }

        /// Apply timestamp handling logic for orders
        fn apply_order_timestamp_handling(&self, order: &mut Order) {
            let current_time = self.get_current_timestamp();

            // For created_at: if input is 0, set to current timestamp
            if order.created_at == 0 {
                order.created_at = current_time;
            }

            // For updated_at: if input is 0, set to current timestamp
            if order.updated_at == 0 {
                order.updated_at = current_time;
            }

            // For deleted_at: if input is 0, keep it as 0 (not deleted)
            // No action needed as 0 means not deleted
        }

        /// Create a new user service from JSON string
        // #[glue::atomic]
        pub fn insert_user_service(&mut self, service_json: String) -> anyhow::Result<String> {
            
            let mut service: UserService = serde_json::from_str(&service_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse service JSON: {}", e))?;
            // Validate required fields
            if service._id.is_empty() {
                return Err(anyhow::anyhow!("Service ID cannot be empty"));
            }

            if self.user_services.contains(&service._id) {
                return Err(anyhow::anyhow!("User service with this ID already exists"));
            }

            // Apply timestamp handling logic
            self.apply_timestamp_handling(&mut service);

            // Ensure deleted_at is 0 for new services
            service.deleted_at = 0;

            self.user_services.insert(&service._id, &service);
            Ok(service._id)
        }

        /// Create multiple user services in a single transaction
        ///
        /// This function enables efficient batch creation of UserService records with:
        /// - Reduced gas costs through single transaction processing
        /// - Graceful handling of partial failures with detailed error reporting
        /// - Validation of each service object before processing
        /// - Duplicate ID detection both within batch and against existing services
        /// - Atomic transaction behavior for critical operations
        ///
        /// # Parameters
        /// * `services_json` - JSON string containing array of UserService objects
        ///   Example: `[{"id": "service1", "amount": 100.0, ...}, {"id": "service2", "amount": 200.0, ...}]`
        ///
        /// # Returns
        /// JSON string containing BatchResult with:
        /// - `created`: Number of successfully created services
        /// - `updated`: Number of updated services (always 0 for create operations)
        /// - `deleted`: Number of deleted services (always 0 for create operations)
        /// - `errors`: Array of error messages for failed service creations
        ///
        /// # Error Handling
        /// - JSON parsing errors return descriptive error messages
        /// - Individual service validation failures are collected and reported
        /// - Duplicate ID conflicts are handled gracefully without stopping the batch
        /// - Critical errors may cause entire batch rollback due to atomic transaction
        pub fn insert_many_user_service(&mut self, services_json: String) -> anyhow::Result<String> {
            // Parse JSON input with detailed error reporting
            let services: Vec<UserService> = serde_json::from_str(&services_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse services JSON: {}. Expected format: [{{\"id\": \"service1\", \"amount\": 100.0, ...}}, ...]", e))?;

            // Validate input is not empty
            if services.is_empty() {
                return Err(anyhow::anyhow!("Services array cannot be empty"));
            }

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            // Track IDs within this batch to detect duplicates
            let mut batch_ids = std::collections::HashSet::new();

            for mut service in services {
                // Validate required fields
                if service._id.is_empty() {
                    result.errors.push("Service ID cannot be empty".to_string());
                    continue;
                }

                // Check for duplicate IDs within the batch
                if !batch_ids.insert(service._id.clone()) {
                    result.errors.push(format!("Duplicate service ID '{}' found within batch", service._id));
                    continue;
                }

                // Check for existing service with same ID
                if self.user_services.contains(&service._id) {
                    result.errors.push(format!("Service with ID '{}' already exists in database", service._id));
                    continue;
                }

                // Validate business logic constraints
                if service.amount < 0.0 {
                    result.errors.push(format!("Service '{}' has invalid negative amount: {}", service._id, service.amount));
                    continue;
                }

                if service.duration < 0 {
                    result.errors.push(format!("Service '{}' has invalid negative duration: {}", service._id, service.duration));
                    continue;
                }

                // Apply timestamp handling logic
                self.apply_timestamp_handling(&mut service);

                // Ensure deleted_at is 0 for new services
                service.deleted_at = 0;

                // Insert the service
                self.user_services.insert(&service._id, &service);
                result.created += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }

                /// Internal implementation for batch updating user services with partial updates
        fn update_many_user_service(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
            let update_params: UserServiceUpdate = serde_json::from_str(&update_many_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceUpdate JSON: {}", e))?;

            let params = update_params.filter;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            // Find services matching the filter criteria
            let mut services_to_update = Vec::new();

            if let Some(ref ids) = params.ids {
                for id in ids {
                    if let Some(service) = self.user_services.get(id) {
                        if self.matches_filters(&service, &params) {
                            services_to_update.push(service);
                        }
                    }
                }
            } else {
                let mut iter = self.user_services.index("user_services_created_at").iter(
                    false,
                    &format!("{:0>19}", 0),
                    &format!("{:9>19}", i64::MAX)
                );
                while iter.next() {
                    let service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service: {}", e))?;
                    if self.matches_filters(&service, &params) {
                        services_to_update.push(service);
                    }
                }
            }

            // Update each matching service with partial data
            for mut service in services_to_update {
                // Parse update data as a generic JSON object for partial updates
                if let Some(update_obj) = update_params.update_data.as_object() {
                    // Update only specified fields
                    for (key, value) in update_obj {
                        match key.as_str() {
                            "duration" => {
                                if let Some(duration) = value.as_i64() {
                                    service.duration = duration;
                                }
                            }
                            "amount" => {
                                if let Some(amount) = value.as_f64() {
                                    service.amount = amount;
                                }
                            }
                            "status" => {
                                if let Some(status) = value.as_str() {
                                    service.status = status.to_string();
                                }
                            }
                            "serviceActivated" => {
                                if let Some(activated) = value.as_bool() {
                                    service.service_activated = activated;
                                }
                            }
                            // Add more fields as needed
                            _ => {} // Ignore unknown fields
                        }
                    }
                }

                // Always update the updated_at timestamp
                service.updated_at = current_time;

                self.user_services.insert(&service._id, &service);
                result.updated += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }

                /// Internal implementation for bulk write operations on user services
        fn bulk_write_user_service(&mut self, bulk_write_json_string: String) -> anyhow::Result<String> {
            let operations: Vec<UserServiceBulkWriteOperation> = serde_json::from_str(&bulk_write_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceBulkWriteOperation JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            for operation in operations {
                match operation.operation_type.as_str() {
                    "insert" => {
                        if let Some(data) = operation.data {
                            match serde_json::from_value::<UserService>(data) {
                                Ok(mut service) => {
                                    if service._id.is_empty() {
                                        result.errors.push("Service ID cannot be empty".to_string());
                                        continue;
                                    }
                                    if self.user_services.contains(&service._id) {
                                        result.errors.push(format!("Service with ID '{}' already exists", service._id));
                                        continue;
                                    }
                                    self.apply_timestamp_handling(&mut service);
                                    service.deleted_at = 0;
                                    self.user_services.insert(&service._id, &service);
                                    result.created += 1;
                                }
                                Err(e) => {
                                    result.errors.push(format!("Failed to parse service data: {}", e));
                                }
                            }
                        }
                    }
                    "update" => {
                        if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                            let update_params = UserServiceUpdate {
                                filter,
                                update_data: data,
                            };
                            let update_json = serde_json::to_string(&update_params)?;
                            match self.update_many_user_service(update_json) {
                                Ok(update_result) => {
                                    if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&update_result) {
                                        result.updated += batch_result.updated;
                                        result.errors.extend(batch_result.errors);
                                    }
                                }
                                Err(e) => {
                                    result.errors.push(format!("Update operation failed: {}", e));
                                }
                            }
                        }
                    }
                    "delete_many" => {
                        if let Some(filter) = operation.filter {
                            let filter_json = serde_json::to_string(&filter)?;
                            match self.delete_many_user_service(filter_json) {
                                Ok(delete_result) => {
                                    if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&delete_result) {
                                        result.deleted += batch_result.deleted;
                                        result.errors.extend(batch_result.errors);
                                    }
                                }
                                Err(e) => {
                                    result.errors.push(format!("Delete operation failed: {}", e));
                                }
                            }
                        }
                    }
                    _ => {
                        result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                    }
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

                /// Internal implementation for deleting a single user service (HARD DELETE)
        fn delete_user_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
            let params: UserServiceQueryParams = serde_json::from_str(&filter_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceQueryParams JSON: {}", e))?;

            // If IDs are provided, delete the first matching one
            if let Some(ref ids) = params.ids {
                if let Some(id) = ids.first() {
                    if self.user_services.contains(id) {
                        self.user_services.remove(id);
                        return Ok(format!("{{\"deleted\": 1}}"));
                    }
                }
            }

            // Find the first matching service
            let mut iter = self.user_services.index("user_services_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service: {}", e))?;
                if self.matches_filters(&service, &params) {
                    // Hard delete: completely remove from storage
                    self.user_services.remove(&service._id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }

            Ok(format!("{{\"deleted\": 0}}"))
        }

         /// Internal implementation for batch deleting user services
        fn delete_many_user_service(&mut self, filter_json_string: String) -> anyhow::Result<String> {
            let params: UserServiceQueryParams = serde_json::from_str(&filter_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse UserServiceQueryParams JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            // No longer need current_time for hard delete

            // Find services matching the filter criteria
            let mut services_to_delete = Vec::new();

            if let Some(ref ids) = params.ids {
                for id in ids {
                    if let Some(service) = self.user_services.get(id) {
                        if self.matches_filters(&service, &params) {
                            services_to_delete.push(service);
                        }
                    }
                }
            } else {
                let mut iter = self.user_services.index("user_services_created_at").iter(
                    false,
                    &format!("{:0>19}", 0),
                    &format!("{:9>19}", i64::MAX)
                );
                while iter.next() {
                    let service = iter.value().map_err(|e| anyhow::anyhow!("Failed to get service: {}", e))?;
                    if self.matches_filters(&service, &params) {
                        services_to_delete.push(service);
                    }
                }
            }

            // Delete each matching service (HARD DELETE)
            for service in services_to_delete {
                // Hard delete: completely remove from storage
                self.user_services.remove(&service._id);
                result.deleted += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Get a single user service by ID
        pub fn get_user_service(&self, id: String) -> anyhow::Result<String> {
            let service = self.user_services.get(&id);
            match service {
                Some(service) => Ok(serde_json::to_string(&service)?),
                None => Err(anyhow::anyhow!("User service not found")),
            }
        }

        /// Update an existing user service from JSON string
        pub fn update_user_service(&mut self, service_json: String) -> anyhow::Result<()> {
            let mut service: UserService = serde_json::from_str(&service_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse service JSON: {}", e))?;

            // Validate required fields
            if service._id.is_empty() {
                return Err(anyhow::anyhow!("Service ID cannot be empty"));
            }

            if !self.user_services.contains(&service._id) {
                return Err(anyhow::anyhow!("User service not found"));
            }

            // Apply timestamp handling logic for updates
            if service.updated_at == 0 {
                service.updated_at = self.get_current_timestamp();
            }

            self.user_services.insert(&service._id, &service);
            Ok(())
        }

        /// Enhanced query user services with comprehensive filtering, pagination, and sorting
        pub fn find_user_service(&self, params_json: String) -> anyhow::Result<String> {
            let params: UserServiceQueryParams = serde_json::from_str(&params_json)?;

            let mut services = Vec::new();
            let mut count = 0u64;
            let limit = params.limit.unwrap_or(10);
            let offset = params.offset.unwrap_or(0);

            // Determine the most efficient index to use based on provided filters
            // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
            if let Some(ref ids) = params.ids {
                // Batch ID query - fetch services by IDs directly
                for id in ids {
                    if let Some(service) = self.user_services.get(id) {
                        if self.matches_filters(&service, &params) {
                            if count < offset {
                                count += 1;
                                continue;
                            }
                            if services.len() >= limit as usize {
                                break;
                            }
                            services.push(service);
                            count += 1;
                        }
                    }
                }
            } else if let (Some(ref address), Some(ref status)) = (&params.address, &params.status) {
                // Use composite address_status index with efficient sorting
                let key_prefix = format!("{}-{}", address, status);
                self.query_with_composite_index_sorting("user_services_address_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref address), Some(service_activated)) = (&params.address, &params.service_activated) {
                // Use composite address_service_activated index with efficient sorting
                let key_prefix = format!("{}-{}", address, service_activated);
                self.query_with_composite_index_sorting("user_services_address_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref provider), Some(ref status)) = (&params.provider, &params.status) {
                // Use composite provider_status index with efficient sorting
                let key_prefix = format!("{}-{}", provider, status);
                self.query_with_composite_index_sorting("user_services_provider_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref address) = params.address {
                // Use address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_address", address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider) = params.provider {
                // Use provider index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider", provider, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider_address) = params.provider_address {
                // Use provider_address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider_address", provider_address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref status) = params.status {
                // Use status index with efficient sorting
                self.query_with_composite_index_sorting("user_services_status", status, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(service_activated) = params.service_activated {
                // Use service_activated index with efficient sorting
                let key_prefix = service_activated.to_string();
                self.query_with_composite_index_sorting("user_services_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref service_id) = params.service_id {
                // Use service_id index with efficient sorting
                self.query_with_composite_index_sorting("user_services_service_id", service_id, &params, &mut services, &mut count, limit, offset)?;
            } else {
                // No specific index, use created_at index with efficient sorting
                self.query_with_created_at_sorting(&params, &mut services, &mut count, limit, offset)?;
            }
            // Note: Sorting is now handled efficiently during iteration, no post-processing needed
            Ok(serde_json::to_string(&services)?)
        }

        /// Count user services with advanced filtering
        pub fn count_user_service(&self, params_json: String) -> anyhow::Result<String> {

            let params: UserServiceQueryParams = serde_json::from_str(&params_json)?;

            let mut services = Vec::new();
            let mut count = 0u64;
            let limit = params.limit.unwrap_or(10);
            let offset = params.offset.unwrap_or(0);

            // Determine the most efficient index to use based on provided filters
            // Priority: Handle batch ID queries first, then use composite indexes when multiple filters are present
            if let Some(ref ids) = params.ids {
                // Batch ID query - fetch services by IDs directly
                for id in ids {
                    if let Some(service) = self.user_services.get(id) {
                        if self.matches_filters(&service, &params) {
                            if count < offset {
                                count += 1;
                                continue;
                            }
                            if services.len() >= limit as usize {
                                break;
                            }
                            services.push(service);
                            count += 1;
                        }
                    }
                }
            } else if let (Some(ref address), Some(ref status)) = (&params.address, &params.status) {
                // Use composite address_status index with efficient sorting
                let key_prefix = format!("{}-{}", address, status);
                self.query_with_composite_index_sorting("user_services_address_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref address), Some(service_activated)) = (&params.address, &params.service_activated) {
                // Use composite address_service_activated index with efficient sorting
                let key_prefix = format!("{}-{}", address, service_activated);
                self.query_with_composite_index_sorting("user_services_address_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let (Some(ref provider), Some(ref status)) = (&params.provider, &params.status) {
                // Use composite provider_status index with efficient sorting
                let key_prefix = format!("{}-{}", provider, status);
                self.query_with_composite_index_sorting("user_services_provider_status", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref address) = params.address {
                // Use address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_address", address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider) = params.provider {
                // Use provider index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider", provider, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref provider_address) = params.provider_address {
                // Use provider_address index with efficient sorting
                self.query_with_composite_index_sorting("user_services_provider_address", provider_address, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref status) = params.status {
                // Use status index with efficient sorting
                self.query_with_composite_index_sorting("user_services_status", status, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(service_activated) = params.service_activated {
                // Use service_activated index with efficient sorting
                let key_prefix = service_activated.to_string();
                self.query_with_composite_index_sorting("user_services_service_activated", &key_prefix, &params, &mut services, &mut count, limit, offset)?;
            } else if let Some(ref service_id) = params.service_id {
                // Use service_id index with efficient sorting
                self.query_with_composite_index_sorting("user_services_service_id", service_id, &params, &mut services, &mut count, limit, offset)?;
            } else {
                // No specific index, use created_at index with efficient sorting
                self.query_with_created_at_sorting(&params, &mut services, &mut count, limit, offset)?;
            }
            // Note: Sorting is now handled efficiently during iteration, no post-processing needed
            Ok(count.to_string())
        }

        /// Helper function to check if a service matches the given filters
        fn matches_filters(&self, service: &UserService, params: &UserServiceQueryParams) -> bool {
            // Skip deleted services unless specifically querying for them
            if service.deleted_at > 0 {
                return false;
            }

            // Check IDs filter (batch ID query)
            if let Some(ref ids) = params.ids {
                if !ids.contains(&service._id) {
                    return false;
                }
            }

            // Check service_id filter
            if let Some(ref service_id) = params.service_id {
                if service.service_id != *service_id {
                    return false;
                }
            }

            // Check address filter
            if let Some(ref address) = params.address {
                if service.address != *address {
                    return false;
                }
            }

            // Check provider filter
            if let Some(ref provider) = params.provider {
                if service.provider != *provider {
                    return false;
                }
            }

            // Check provider_address filter
            if let Some(ref provider_address) = params.provider_address {
                if service.provider_address != *provider_address {
                    return false;
                }
            }

            // Check status filter
            if let Some(ref status) = params.status {
                if service.status != *status {
                    return false;
                }
            }

            // Check service_activated filter
            if let Some(service_activated) = params.service_activated {
                if service.service_activated != service_activated {
                    return false;
                }
            }

            // Check created_at time range
            if let Some(start) = params.created_at_start {
                if service.created_at < start {
                    return false;
                }
            }
            if let Some(end) = params.created_at_end {
                if service.created_at > end {
                    return false;
                }
            }

            // Check updated_at time range
            if let Some(start) = params.updated_at_start {
                if service.updated_at < start {
                    return false;
                }
            }
            if let Some(end) = params.updated_at_end {
                if service.updated_at > end {
                    return false;
                }
            }

            true
        }

        /// Efficient query using created_at index with built-in sorting
        fn query_with_created_at_sorting(
            &self,
            params: &UserServiceQueryParams,
            services: &mut Vec<UserService>,
            count: &mut u64,
            limit: u64,
            offset: u64,
        ) -> anyhow::Result<()> {
            // Determine sort order from params
            let sort_desc = params.sort_desc.unwrap_or(false);

            // Set up iteration range based on sort order
            let (start_key, end_key, reverse) = if sort_desc {
                // Descending: newest first (reverse iteration from max to min)
                (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
            } else {
                // Ascending: oldest first (forward iteration from min to max)
                (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
            };

            let mut iter = self.user_services.index("user_services_created_at").iter(reverse, &start_key, &end_key);
            while iter.next() {
                println!("iter.key:{:?}",iter.key());
                let service = iter.value()?;
                if self.matches_filters(&service, &params) {
                    if *count < offset {
                        *count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }
                    services.push(service);
                    *count += 1;
                }
            }

            Ok(())
        }

        /// Enhanced query with efficient index-based sorting for composite indexes
        fn query_with_composite_index_sorting(
            &self,
            index_name: &str,
            key_prefix: &str,
            params: &UserServiceQueryParams,
            services: &mut Vec<UserService>,
            count: &mut u64,
            limit: u64,
            offset: u64,
        ) -> anyhow::Result<()> {
            // Determine sort order from params
            let sort_desc = params.sort_desc.unwrap_or(false);

            // Composite indexes already include created_at in the key, so we can use reverse iteration
            let (start_key, end_key, reverse) = if sort_desc {
                // Descending: newest first
                (format!("{}-{:9>19}", key_prefix, i64::MAX), format!("{}-{:0>19}", key_prefix, 0), true)
            } else {
                // Ascending: oldest first
                (format!("{}-{:0>19}", key_prefix, 0), format!("{}-{:9>19}", key_prefix, i64::MAX), false)
            };
            println!("index_name:{:?}",index_name);
            println!("start_key:{:?}",start_key);
            println!("end_key:{:?}",end_key);
            let mut iter = self.user_services.index(index_name).iter(reverse, &start_key, &end_key);
            while iter.next() {
                let service = iter.value()?;
                println!("iter.key:{:?}",iter.key());
                if self.matches_filters(&service, &params) {
                    if *count < offset {
                        *count += 1;
                        continue;
                    }
                    if services.len() >= limit as usize {
                        break;
                    }
                    services.push(service);
                    *count += 1;
                }
            }
            

            Ok(())
        }

        /// Helper function for counting orders using composite indexes
        fn count_with_composite_index(
            &self,
            index_name: &str,
            key_prefix: &str,
            params: &OrderQueryParam,
            count: &mut u64,
        ) -> anyhow::Result<()> {
            let (start_key, end_key) = (
                format!("{}-{:0>19}", key_prefix, 0),
                format!("{}-{:9>19}", key_prefix, i64::MAX),
            );

            let mut iter = self.orders.index(index_name).iter(false, &start_key, &end_key);
            while iter.next() {
                let order = iter.value()?;
                if self.matches_order_filters(&order, params) {
                    *count += 1;
                }
            }

            Ok(())
        }

        /// Helper function for counting orders using created_at index
        fn count_with_created_at_index(
            &self,
            params: &OrderQueryParam,
            count: &mut u64,
        ) -> anyhow::Result<()> {
            let mut iter = self.orders.index("order_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order = iter.value()?;
                if self.matches_order_filters(&order, params) {
                    *count += 1;
                }
            }

            Ok(())
        }

        /// Helper function for querying orders with index optimization and pagination
        fn query_orders_with_index(
            &self,
            params: &OrderQueryParam,
            orders: &mut Vec<Order>,
            count: &mut u64,
            limit: u64,
            offset: u64,
        ) -> anyhow::Result<()> {
            // Determine sort order from params
            let sort_desc = params.sort_desc.unwrap_or(false);

            // Choose the most efficient index based on filters
            if !params.address.is_empty() && !params.statuses.is_empty() {
                // Use composite address_status index
                for status in &params.statuses {
                    let key_prefix = format!("{}-{}", params.address, status);
                    self.query_with_order_composite_index("order_address_status", &key_prefix, params, orders, count, limit, offset, sort_desc)?;
                    if orders.len() >= limit as usize {
                        break;
                    }
                }
            } else if !params.address.is_empty() {
                // Use address index
                self.query_with_order_composite_index("order_address", &params.address, params, orders, count, limit, offset, sort_desc)?;
            } else if !params.recipient.is_empty() {
                // Use recipient index
                self.query_with_order_composite_index("order_recipient", &params.recipient, params, orders, count, limit, offset, sort_desc)?;
            } else if !params.service.is_empty() {
                // Use provider index (service maps to provider)
                self.query_with_order_composite_index("order_provider", &params.service, params, orders, count, limit, offset, sort_desc)?;
            } else if !params.order_type.is_empty() {
                // Use order_type index
                self.query_with_order_composite_index("order_type", &params.order_type, params, orders, count, limit, offset, sort_desc)?;
            } else if !params.statuses.is_empty() {
                // Use status index
                for status in &params.statuses {
                    self.query_with_order_composite_index("order_status", status, params, orders, count, limit, offset, sort_desc)?;
                    if orders.len() >= limit as usize {
                        break;
                    }
                }
            } else {
                // Use created_at index
                self.query_with_order_created_at_index(params, orders, count, limit, offset, sort_desc)?;
            }

            Ok(())
        }

        /// Helper function for querying orders with composite index and sorting
        fn query_with_order_composite_index(
            &self,
            index_name: &str,
            key_prefix: &str,
            params: &OrderQueryParam,
            orders: &mut Vec<Order>,
            count: &mut u64,
            limit: u64,
            offset: u64,
            sort_desc: bool,
        ) -> anyhow::Result<()> {
            let (start_key, end_key, reverse) = if sort_desc {
                // Descending: newest first
                (format!("{}-{:9>19}", key_prefix, i64::MAX), format!("{}-{:0>19}", key_prefix, 0), true)
            } else {
                // Ascending: oldest first
                (format!("{}-{:0>19}", key_prefix, 0), format!("{}-{:9>19}", key_prefix, i64::MAX), false)
            };

            let mut iter = self.orders.index(index_name).iter(reverse, &start_key, &end_key);
            while iter.next() {
                let order = iter.value()?;
                if self.matches_order_filters(&order, params) {
                    if *count < offset {
                        *count += 1;
                        continue;
                    }
                    if orders.len() >= limit as usize {
                        break;
                    }
                    orders.push(order);
                    *count += 1;
                }
            }

            Ok(())
        }

        /// Helper function for querying orders with created_at index and sorting
        fn query_with_order_created_at_index(
            &self,
            params: &OrderQueryParam,
            orders: &mut Vec<Order>,
            count: &mut u64,
            limit: u64,
            offset: u64,
            sort_desc: bool,
        ) -> anyhow::Result<()> {
            let (start_key, end_key, reverse) = if sort_desc {
                // Descending: newest first
                (format!("{:9>19}", i64::MAX), format!("{:0>19}", 0), true)
            } else {
                // Ascending: oldest first
                (format!("{:0>19}", 0), format!("{:9>19}", i64::MAX), false)
            };

            let mut iter = self.orders.index("order_created_at").iter(reverse, &start_key, &end_key);
            while iter.next() {
                let order = iter.value()?;
                if self.matches_order_filters(&order, params) {
                    if *count < offset {
                        *count += 1;
                        continue;
                    }
                    if orders.len() >= limit as usize {
                        break;
                    }
                    orders.push(order);
                    *count += 1;
                }
            }

            Ok(())
        }

        // Order functions
        /// Create a new order from JSON string
        pub fn insert_order(&mut self, order_json: String) -> anyhow::Result<String> {
            let mut order: Order = serde_json::from_str(&order_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse order JSON: {}", e))?;

            // Validate required fields
            if order._id.is_empty() {
                return Err(anyhow::anyhow!("Order ID cannot be empty"));
            }

            if self.orders.contains(&order._id) {
                return Err(anyhow::anyhow!("Order with this ID already exists"));
            }

            // Apply timestamp handling logic
            self.apply_order_timestamp_handling(&mut order);

            // Ensure deleted_at is 0 for new orders
            order.deleted_at = 0;

            self.orders.insert(&order._id, &order);
            Ok(order._id)
        }

        /// Internal implementation for batch inserting orders
        fn insert_many_order(&mut self, insert_many_json_string: String) -> anyhow::Result<String> {
            // Parse JSON input with detailed error reporting
            let orders: Vec<Order> = serde_json::from_str(&insert_many_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse orders JSON: {}. Expected format: [{{\"ID\": \"order1\", \"Amount\": 100.0, ...}}, ...]", e))?;

            // Validate input is not empty
            if orders.is_empty() {
                return Err(anyhow::anyhow!("Orders array cannot be empty"));
            }

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            // Track IDs within this batch to detect duplicates
            let mut batch_ids = std::collections::HashSet::new();

            for mut order in orders {
                // Validate required fields
                if order._id.is_empty() {
                    result.errors.push("Order ID cannot be empty".to_string());
                    continue;
                }

                // Check for duplicate IDs within the batch
                if !batch_ids.insert(order._id.clone()) {
                    result.errors.push(format!("Duplicate order ID '{}' found within batch", order._id));
                    continue;
                }

                // Check for existing order with same ID
                if self.orders.contains(&order._id) {
                    result.errors.push(format!("Order with ID '{}' already exists in database", order._id));
                    continue;
                }

                // Validate business logic constraints
                if order.amount < 0.0 {
                    result.errors.push(format!("Order '{}' has invalid negative amount: {}", order._id, order.amount));
                    continue;
                }

                // Apply timestamp handling logic
                self.apply_order_timestamp_handling(&mut order);

                // Ensure deleted_at is 0 for new orders
                order.deleted_at = 0;

                // Insert the order
                self.orders.insert(&order._id, &order);
                result.created += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }

        /// Get a single order by ID
        pub fn get_order(&self, id: String) -> anyhow::Result<String> {
            let order = self.orders.get(&id);
            match order {
                Some(order) => Ok(serde_json::to_string(&order)?),
                None => Err(anyhow::anyhow!("Order not found")),
            }
        }
        /// Internal implementation for batch updating orders with partial updates
        fn update_many_order(&mut self, update_many_json_string: String) -> anyhow::Result<String> {
            let update_params: OrderUpdate = serde_json::from_str(&update_many_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse OrderUpdate JSON: {}", e))?;

            let params = update_params.filter;

            // Use existing update_many_orders function
            let filter_json = serde_json::to_string(&params)?;
            let update_json = serde_json::to_string(&update_params.update_data)?;
            self.update_many_orders(filter_json, update_json)
        }
        /// Update an existing order from JSON string
        pub fn update_order(&mut self, order_json: String) -> anyhow::Result<()> {
            let mut order: Order = serde_json::from_str(&order_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse order JSON: {}", e))?;

            // Validate required fields
            if order._id.is_empty() {
                return Err(anyhow::anyhow!("Order ID cannot be empty"));
            }

            if !self.orders.contains(&order._id) {
                return Err(anyhow::anyhow!("Order not found"));
            }

            // Apply timestamp handling logic for updates
            if order.updated_at == 0 {
                order.updated_at = self.get_current_timestamp();
            }

            self.orders.insert(&order._id, &order);
            Ok(())
        }

        /// Internal implementation for bulk write operations on orders
        fn bulk_write_order(&mut self, bulk_write_json_string: String) -> anyhow::Result<String> {
            let operations: Vec<OrderBulkWriteOperation> = serde_json::from_str(&bulk_write_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse OrderBulkWriteOperation JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            for operation in operations {
                match operation.operation_type.as_str() {
                    "insert" => {
                        if let Some(data) = operation.data {
                            match serde_json::from_value::<Order>(data) {
                                Ok(mut order) => {
                                    if order._id.is_empty() {
                                        result.errors.push("Order ID cannot be empty".to_string());
                                        continue;
                                    }
                                    if self.orders.contains(&order._id) {
                                        result.errors.push(format!("Order with ID '{}' already exists", order._id));
                                        continue;
                                    }
                                    self.apply_order_timestamp_handling(&mut order);
                                    order.deleted_at = 0;
                                    self.orders.insert(&order._id, &order);
                                    result.created += 1;
                                }
                                Err(e) => {
                                    result.errors.push(format!("Failed to parse order data: {}", e));
                                }
                            }
                        }
                    }
                    "update" => {
                        if let (Some(filter), Some(data)) = (operation.filter, operation.data) {
                            let update_params = OrderUpdate {
                                filter,
                                update_data: data,
                            };
                            let update_json = serde_json::to_string(&update_params)?;
                            match self.update_many_order(update_json) {
                                Ok(update_result) => {
                                    if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&update_result) {
                                        result.updated += batch_result.updated;
                                        result.errors.extend(batch_result.errors);
                                    }
                                }
                                Err(e) => {
                                    result.errors.push(format!("Update operation failed: {}", e));
                                }
                            }
                        }
                    }
                    "delete_many" => {
                        if let Some(filter) = operation.filter {
                            let filter_json = serde_json::to_string(&filter)?;
                            match self.delete_many_order(filter_json) {
                                Ok(delete_result) => {
                                    if let Ok(batch_result) = serde_json::from_str::<BatchResult>(&delete_result) {
                                        result.deleted += batch_result.deleted;
                                        result.errors.extend(batch_result.errors);
                                    }
                                }
                                Err(e) => {
                                    result.errors.push(format!("Delete operation failed: {}", e));
                                }
                            }
                        }
                    }
                    _ => {
                        result.errors.push(format!("Unsupported operation type: {}", operation.operation_type));
                    }
                }
            }

            Ok(serde_json::to_string(&result)?)
        }

          /// Internal implementation for deleting a single order (HARD DELETE)
        fn delete_order(&mut self, filter_json_string: String) -> anyhow::Result<String> {
            let params: OrderQueryParam = serde_json::from_str(&filter_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse OrderQueryParam JSON: {}", e))?;

            // If IDs are provided, delete the first matching one
            if !params.ids.is_empty() {
                if let Some(id) = params.ids.first() {
                    if self.orders.contains(id) {
                        // Hard delete: completely remove from storage
                        self.orders.remove(id);
                        return Ok(format!("{{\"deleted\": 1}}"));
                    }
                }
            }

            // Otherwise, find the first order matching the filter and delete it
            let mut iter = self.orders.index("order_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order: {}", e))?;
                if self.matches_order_filters(&order, &params) {
                    // Hard delete: completely remove from storage
                    self.orders.remove(&order._id);
                    return Ok(format!("{{\"deleted\": 1}}"));
                }
            }

            Ok(format!("{{\"deleted\": 0}}"))
        }

                /// Internal implementation for batch deleting orders
        fn delete_many_order(&mut self, filter_json_string: String) -> anyhow::Result<String> {
            let params: OrderQueryParam = serde_json::from_str(&filter_json_string)
                .map_err(|e| anyhow::anyhow!("Failed to parse OrderQueryParam JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            // No longer need current_time for hard delete

            // Find orders matching the filter criteria
            let mut orders_to_delete = Vec::new();

            if !params.ids.is_empty() {
                for id in &params.ids {
                    if let Some(order) = self.orders.get(id) {
                        if self.matches_order_filters(&order, &params) {
                            orders_to_delete.push(order);
                        }
                    }
                }
            } else {
                let mut iter = self.orders.index("order_created_at").iter(
                    false,
                    &format!("{:0>19}", 0),
                    &format!("{:9>19}", i64::MAX)
                );
                while iter.next() {
                    let order = iter.value().map_err(|e| anyhow::anyhow!("Failed to get order: {}", e))?;
                    if self.matches_order_filters(&order, &params) {
                        orders_to_delete.push(order);
                    }
                }
            }

            // Delete each matching order (HARD DELETE)
            for order in orders_to_delete {
                // Hard delete: completely remove from storage
                self.orders.remove(&order._id);
                result.deleted += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }


        /// Helper function to check if an order matches the given filters
        fn matches_order_filters(&self, order: &Order, params: &OrderQueryParam) -> bool {
            // Skip deleted orders unless specifically querying for them
            if order.deleted_at > 0 {
                return false;
            }

            // Check IDs filter (batch ID query)
            if !params.ids.is_empty() {
                if !params.ids.contains(&order._id) {
                    return false;
                }
            }

            // Check address filter
            if !params.address.is_empty() {
                if order.address != params.address {
                    return false;
                }
            }

            // Check recipient filter
            if !params.recipient.is_empty() {
                if order.recipient != params.recipient {
                    return false;
                }
            }

            // Check provider filter (using service field for provider matching)
            if !params.service.is_empty() {
                if order.provider != params.service {
                    return false;
                }
            }

            // Check order_type filter
            if !params.order_type.is_empty() {
                if order.order_type != params.order_type {
                    return false;
                }
            }

            // Check statuses filter
            if !params.statuses.is_empty() {
                if !params.statuses.contains(&order.status) {
                    return false;
                }
            }

            // Check time range filter (TSStart and TSEnd)
            if let (Some(ts_start), Some(ts_end)) = (params.ts_start, params.ts_end) {
                // Filter records where (paidTS OR filedTS) is greater than TSStart AND (paidTS OR filedTS) is less than TSEnd
                let relevant_ts = if order.paid_ts > 0 { order.paid_ts } else { order.filed_ts };
                if relevant_ts <= ts_start || relevant_ts >= ts_end {
                    return false;
                }
            }

            true
        }

        /// Count orders with advanced filtering
        pub fn count_order(&self, params_json: String) -> anyhow::Result<String> {
            let params: OrderQueryParam = serde_json::from_str(&params_json)?;
            let mut count = 0u64;

            // Determine the most efficient index to use based on provided filters
            if !params.ids.is_empty() {
                // Batch ID query - count orders by IDs directly
                for id in &params.ids {
                    if let Some(order) = self.orders.get(id) {
                        if self.matches_order_filters(&order, &params) {
                            count += 1;
                        }
                    }
                }
            } else if !params.address.is_empty() && !params.statuses.is_empty() {
                // Use composite address_status index for efficient counting
                for status in &params.statuses {
                    let key_prefix = format!("{}-{}", params.address, status);
                    self.count_with_composite_index("order_address_status", &key_prefix, &params, &mut count)?;
                }
            } else if !params.address.is_empty() {
                // Use address index for efficient counting
                self.count_with_composite_index("order_address", &params.address, &params, &mut count)?;
            } else if !params.recipient.is_empty() {
                // Use recipient index for efficient counting
                self.count_with_composite_index("order_recipient", &params.recipient, &params, &mut count)?;
            } else if !params.service.is_empty() {
                // Use provider index for efficient counting (service maps to provider)
                self.count_with_composite_index("order_provider", &params.service, &params, &mut count)?;
            } else if !params.order_type.is_empty() {
                // Use order_type index for efficient counting
                self.count_with_composite_index("order_type", &params.order_type, &params, &mut count)?;
            } else if !params.statuses.is_empty() {
                // Use status index for efficient counting
                for status in &params.statuses {
                    self.count_with_composite_index("order_status", status, &params, &mut count)?;
                }
            } else {
                // No specific index, use created_at index for counting
                self.count_with_created_at_index(&params, &mut count)?;
            }

            Ok(count.to_string())
        }

        /// Query orders with filtering
        pub fn find_order(&self, params_json: String) -> anyhow::Result<String> {
            let params: OrderQueryParam = serde_json::from_str(&params_json)?;

            let mut orders = Vec::new();
            let mut count = 0u64;
            let limit = params.limit.unwrap_or(100); // Default limit
            let offset = params.offset.unwrap_or(0);

            // Use direct ID lookup if IDs are provided
            if !params.ids.is_empty() {
                for id in &params.ids {
                    if let Some(order) = self.orders.get(id) {
                        if self.matches_order_filters(&order, &params) {
                            if count < offset {
                                count += 1;
                                continue;
                            }
                            if orders.len() >= limit as usize {
                                break;
                            }
                            orders.push(order);
                            count += 1;
                        }
                    }
                }
            } else {
                // Use index-based iteration with pagination support
                self.query_orders_with_index(&params, &mut orders, &mut count, limit, offset)?;
            }

            Ok(serde_json::to_string(&orders)?)
        }

        /// Get distinct values for a specific field in orders
        pub fn distinct_orders(&self, params_json: String) -> anyhow::Result<String> {
            let params: OrderDistinctParam = serde_json::from_str(&params_json)?;

            let mut distinct_values = std::collections::HashSet::new();

            // Iterate through all orders
            let mut iter = self.orders.index("order_created_at").iter(
                false,
                &format!("{:0>19}", 0),
                &format!("{:9>19}", i64::MAX)
            );
            while iter.next() {
                let order = iter.value()?;

                // Skip deleted orders
                if order.deleted_at > 0 {
                    continue;
                }

                // Apply address filter
                if !params.address.is_empty() && order.address != params.address {
                    continue;
                }

                // Apply statuses filter
                if !params.statuses.is_empty() && !params.statuses.contains(&order.status) {
                    continue;
                }

                // Extract the distinct field value
                let field_value = match params.distinct_field.as_str() {
                    "status" => order.status.clone(),
                    "type" => order.order_type.clone(),
                    "provider" => order.provider.clone(),
                    "address" => order.address.clone(),
                    "recipient" => order.recipient.clone(),
                    // Support both old and new field names for backward compatibility
                    "Status" => order.status.clone(),
                    "Type" => order.order_type.clone(),
                    "Provider" => order.provider.clone(),
                    "Address" => order.address.clone(),
                    "Recipient" => order.recipient.clone(),
                    _ => continue, // Skip unknown fields
                };

                distinct_values.insert(field_value);
            }

            let result: Vec<String> = distinct_values.into_iter().collect();
            Ok(serde_json::to_string(&result)?)
        }

        /// Update many orders based on filter criteria
        pub fn update_many_orders(&mut self, filter_json: String, update_json: String) -> anyhow::Result<String> {
            let filter_params: OrderQueryParam = serde_json::from_str(&filter_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse filter JSON: {}", e))?;
            let update_order: Order = serde_json::from_str(&update_json)
                .map_err(|e| anyhow::anyhow!("Failed to parse update JSON: {}", e))?;

            let mut result = BatchResult {
                created: 0,
                updated: 0,
                deleted: 0,
                errors: Vec::new(),
            };

            let current_time = self.get_current_timestamp();

            // Find orders matching the filter criteria
            let mut orders_to_update = Vec::new();

            if !filter_params.ids.is_empty() {
                for id in &filter_params.ids {
                    if let Some(order) = self.orders.get(id) {
                        if self.matches_order_filters(&order, &filter_params) {
                            orders_to_update.push(order);
                        }
                    }
                }
            } else {
                let mut iter = self.orders.index("order_created_at").iter(
                    false,
                    &format!("{:0>19}", 0),
                    &format!("{:9>19}", i64::MAX)
                );
                while iter.next() {
                    let order = iter.value()?;
                    if self.matches_order_filters(&order, &filter_params) {
                        orders_to_update.push(order);
                    }
                }
            }

            // Update each matching order
            for mut order in orders_to_update {
                // Update only non-empty/valid fields from update_order
                if !update_order.order_type.is_empty() {
                    order.order_type = update_order.order_type.clone();
                }
                if update_order.amount > 0.0 {
                    order.amount = update_order.amount;
                }
                if update_order.amount_paid > 0.0 {
                    order.amount_paid = update_order.amount_paid;
                }
                if !update_order.provider.is_empty() {
                    order.provider = update_order.provider.clone();
                }
                if !update_order.address.is_empty() {
                    order.address = update_order.address.clone();
                }
                if !update_order.recipient.is_empty() {
                    order.recipient = update_order.recipient.clone();
                }
                if !update_order.status.is_empty() {
                    order.status = update_order.status.clone();
                }
                if update_order.last_payment_ts > 0 {
                    order.last_payment_ts = update_order.last_payment_ts;
                }
                if update_order.paid_ts > 0 {
                    order.paid_ts = update_order.paid_ts;
                }
                if update_order.filed_ts > 0 {
                    order.filed_ts = update_order.filed_ts;
                }
                if !update_order.public_key.is_empty() {
                    order.public_key = update_order.public_key.clone();
                }
                if !update_order.user_service_ids.is_empty() {
                    order.user_service_ids = update_order.user_service_ids.clone();
                }
                if !update_order.items.is_empty() {
                    order.items = update_order.items.clone();
                }

                // Always update the updated_at timestamp
                order.updated_at = current_time;

                self.orders.insert(&order._id, &order);
                result.updated += 1;
            }

            Ok(serde_json::to_string(&result)?)
        }






        // ========== UNIFIED INTERFACE FUNCTIONS ==========

        /// Unified find function for all tables
        /// Replaces query functions for both user_service and order tables
        #[glue::readonly]
        pub fn find(&self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.find_user_service(filter_json_string),
                "order" => self.find_order(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified count function for all tables
        #[glue::readonly]
        pub fn count(&self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.count_user_service(filter_json_string),
                "order" => self.count_order(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified get function for all tables
        /// Retrieves a single record by ID
        #[glue::readonly]
        pub fn get(&self, table_name: String, id: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.get_user_service(id),
                "order" => self.get_order(id),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified update function for all tables
        /// Updates a record with the provided data
        #[glue::atomic]
        pub fn update(&mut self, table_name: String, update_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => {
                    self.update_user_service(update_json_string)?;
                    Ok("{}".to_string())
                },
                "order" => {
                    self.update_order(update_json_string)?;
                    Ok("{}".to_string())
                },
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified insert function for all tables
        /// Replaces create functions for both user_service and order tables
        #[glue::atomic]
        pub fn insert(&mut self, table_name: String, insert_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.insert_user_service(insert_json_string),
                "order" => self.insert_order(insert_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified insert_many function for all tables
        /// Replaces createMany functions for both user_service and order tables
        #[glue::atomic]
        pub fn insert_many(&mut self, table_name: String, insert_many_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.insert_many_user_service(insert_many_json_string),
                "order" => self.insert_many_order(insert_many_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified update_many function for all tables
        /// Executes partial field updates (only updates non-empty specified fields)
        #[glue::atomic]
        pub fn update_many(&mut self, table_name: String, update_many_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.update_many_user_service(update_many_json_string),
                "order" => self.update_many_order(update_many_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified bulk_write function for all tables
        /// Supports batch operations: delete_many, insert, update
        #[glue::atomic]
        pub fn bulk_write(&mut self, table_name: String, bulk_write_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.bulk_write_user_service(bulk_write_json_string),
                "order" => self.bulk_write_order(bulk_write_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified delete function for all tables
        /// Deletes a single record matching the filter conditions
        #[glue::atomic]
        pub fn delete(&mut self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.delete_user_service(filter_json_string),
                "order" => self.delete_order(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }

        /// Unified delete_many function for all tables
        /// Deletes multiple records matching the filter conditions
        #[glue::atomic]
        pub fn delete_many(&mut self, table_name: String, filter_json_string: String) -> anyhow::Result<String> {
            match table_name.as_str() {
                "user_service" => self.delete_many_user_service(filter_json_string),
                "order" => self.delete_many_order(filter_json_string),
                _ => Err(anyhow::anyhow!("Unsupported table name: {}", table_name)),
            }
        }
    }
}