{"_id": "json_update_test_service", "duration": 3600, "amount": 250.0, "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d", "provider": "json_update_provider", "providerAddress": "0xprovider_address_123", "address": "0xjson_update_address", "serviceID": "service_type_compute", "serviceActivated": false, "status": "suspended", "serviceOptions": {"cpu": "8", "memory": "16GB", "storage": "200GB"}, "createdAt": 0, "updatedAt": 0, "deletedAt": 0, "endAt": 0, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "compute", "createdAddr": "0xjson_update_address", "labelHash": "0x123456789abcdef"}