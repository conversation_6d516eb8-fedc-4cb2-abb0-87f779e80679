[{"_id": "json_insert_many_test_1", "duration": 3600, "amount": 100.0, "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d", "provider": "json_insert_many_provider", "providerAddress": "0xprovider_address_123", "address": "0xjson_insert_many_address", "serviceID": "service_type_compute", "serviceActivated": true, "status": "active", "serviceOptions": {"cpu": "4", "memory": "8GB", "storage": "100GB"}, "createdAt": 0, "updatedAt": 0, "deletedAt": 0, "endAt": 0, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "compute", "createdAddr": "0xjson_insert_many_address", "labelHash": "0x123456789abcdef"}, {"_id": "json_insert_many_test_2", "duration": 7200, "amount": 200.0, "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d", "provider": "json_insert_many_provider", "providerAddress": "0xprovider_address_123", "address": "0xjson_insert_many_address", "serviceID": "service_type_storage", "serviceActivated": false, "status": "inactive", "serviceOptions": {"cpu": "2", "memory": "4GB", "storage": "50GB"}, "createdAt": 0, "updatedAt": 0, "deletedAt": 0, "endAt": 0, "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0, "serviceDoneTS": 0, "serviceRefundTS": 0, "service": "storage", "createdAddr": "0xjson_insert_many_address", "labelHash": "0x987654321fedcba"}]