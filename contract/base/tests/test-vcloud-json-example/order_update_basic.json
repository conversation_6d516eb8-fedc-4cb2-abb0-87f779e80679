{"_id": "json_update_test_order", "createdAt": 0, "updatedAt": 0, "deletedAt": 0, "type": "service_purchase", "amount": 750.0, "amountPaid": 750.0, "provider": "json_update_order_provider", "address": "0xjson_update_order_address", "recipient": "0xrecipient_address", "status": "paid", "lastPaymentTS": 0, "paidTS": 0, "filedTS": 0, "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d", "userServiceIDs": [], "items": []}