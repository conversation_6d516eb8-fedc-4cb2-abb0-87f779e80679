import json
import os
import pytest
from vm import ContractTester

# 初始化合约测试器
vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    """自动注册合约"""
    vcloudClient.constructor()


def load_json_file(filename):
    """加载JSON文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_dir = os.path.join(current_dir, "test-vcloud-json-example")
    file_path = os.path.join(json_dir, filename)
    
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def create_test_data_for_operation(table_name, operation, test_id_suffix=""):
    """为特定操作创建测试数据"""
    if table_name == "user_service":
        return {
            "_id": f"test_{operation}_{test_id_suffix}",
            "duration": 3600,
            "amount": 100.0,
            "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
            "provider": f"test_{operation}_provider",
            "providerAddress": "0xprovider_address_123",
            "address": f"0xtest_{operation}_address",
            "serviceID": "service_type_compute",
            "serviceActivated": True,
            "status": "active",
            "serviceOptions": {"cpu": "4", "memory": "8GB", "storage": "100GB"},
            "createdAt": 0, "updatedAt": 0, "deletedAt": 0, "endAt": 0,
            "serviceActivateTS": 0, "serviceRunningTS": 0, "serviceAbortTS": 0,
            "serviceDoneTS": 0, "serviceRefundTS": 0,
            "service": "compute",
            "createdAddr": f"0xtest_{operation}_address",
            "labelHash": "0x123456789abcdef"
        }
    elif table_name == "order":
        return {
            "_id": f"test_{operation}_{test_id_suffix}",
            "createdAt": 0, "updatedAt": 0, "deletedAt": 0,
            "type": "service_purchase",
            "amount": 500.0, "amountPaid": 0.0,
            "provider": f"test_{operation}_provider",
            "address": f"0xtest_{operation}_address",
            "recipient": "0xrecipient_address",
            "status": "pending",
            "lastPaymentTS": 0, "paidTS": 0, "filedTS": 0,
            "publicKey": "0x2eae53506943400c7eecd1c3a8569073f0b2cf3a664f6221e13b8bd1ff51c12d",
            "userServiceIDs": [], "items": []
        }


# ========== INSERT 操作测试 ==========

def test_json_user_service_insert():
    """测试用户服务插入操作"""
    data = load_json_file("user_service_insert_basic.json")
    data_json = json.dumps(data)
    
    result, err = vcloudClient.execute("insert", str, "user_service", data_json)
    assert err is None
    assert result == data["_id"]


def test_json_order_insert():
    """测试订单插入操作"""
    data = load_json_file("order_insert_basic.json")
    data_json = json.dumps(data)
    
    result, err = vcloudClient.execute("insert", str, "order", data_json)
    assert err is None
    assert result == data["_id"]


# ========== INSERT_MANY 操作测试 ==========

def test_json_user_service_insert_many():
    """测试用户服务批量插入操作"""
    data = load_json_file("user_service_insert_many.json")
    data_json = json.dumps(data)
    
    result, err = vcloudClient.execute("insert_many", str, "user_service", data_json)
    assert err is None
    
    batch_result = json.loads(result)
    assert batch_result["created"] == 2
    assert len(batch_result["errors"]) == 0


def test_json_order_insert_many():
    """测试订单批量插入操作"""
    data = load_json_file("order_insert_many.json")
    data_json = json.dumps(data)
    
    result, err = vcloudClient.execute("insert_many", str, "order", data_json)
    assert err is None
    
    batch_result = json.loads(result)
    assert batch_result["created"] == 2
    assert len(batch_result["errors"]) == 0


# ========== FIND 操作测试 ==========

def test_json_user_service_find():
    """测试用户服务查找操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "find")
    test_data["address"] = "0xjson_find_address"
    test_data["status"] = "active"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None
    
    # 使用JSON参数进行查找
    filter_data = load_json_file("user_service_find_by_address_status.json")
    filter_json = json.dumps(filter_data)
    
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    
    found_services = json.loads(result)
    assert len(found_services) >= 1
    assert any(s["_id"] == test_data["_id"] for s in found_services)


def test_json_order_find():
    """测试订单查找操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("order", "find")
    test_data["address"] = "0xjson_find_order_address"
    test_data["status"] = "pending"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "order", test_data_json)
    assert err is None
    
    # 使用JSON参数进行查找
    filter_data = load_json_file("order_find_by_address_statuses.json")
    filter_json = json.dumps(filter_data)
    
    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None
    
    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    assert any(o["_id"] == test_data["_id"] for o in found_orders)


# ========== COUNT 操作测试 ==========

def test_json_user_service_count():
    """测试用户服务计数操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "count")
    test_data["address"] = "0xjson_count_address"
    test_data["status"] = "active"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None
    
    # 使用JSON参数进行计数
    filter_data = load_json_file("user_service_count_by_address_status.json")
    filter_json = json.dumps(filter_data)
    
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    
    count = int(result)
    assert count >= 1


def test_json_order_count():
    """测试订单计数操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("order", "count")
    test_data["address"] = "0xjson_count_order_address"
    test_data["status"] = "pending"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "order", test_data_json)
    assert err is None
    
    # 使用JSON参数进行计数
    filter_data = load_json_file("order_count_by_address_statuses.json")
    filter_json = json.dumps(filter_data)
    
    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None
    
    count = int(result)
    assert count >= 1


# ========== GET 操作测试 ==========

def test_json_user_service_get():
    """测试用户服务获取操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "get")
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None
    
    # 获取数据
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", test_data["_id"])
    assert err is None
    
    retrieved_service = json.loads(result)
    assert retrieved_service["_id"] == test_data["_id"]
    assert retrieved_service["amount"] == test_data["amount"]


def test_json_order_get():
    """测试订单获取操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("order", "get")
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "order", test_data_json)
    assert err is None
    
    # 获取数据
    result, err = vcloudClient.executeReadOnly("get", str, "order", test_data["_id"])
    assert err is None
    
    retrieved_order = json.loads(result)
    assert retrieved_order["_id"] == test_data["_id"]
    assert retrieved_order["amount"] == test_data["amount"]


# ========== UPDATE 操作测试 ==========

def test_json_user_service_update():
    """测试用户服务更新操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "update")
    test_data["_id"] = "json_update_test_service"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None

    # 使用JSON参数进行更新
    update_data = load_json_file("user_service_update_basic.json")
    update_json = json.dumps(update_data)

    result, err = vcloudClient.execute("update", str, "user_service", update_json)
    assert err is None

    # 验证更新结果
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", update_data["_id"])
    assert err is None
    updated_service = json.loads(result)
    assert updated_service["amount"] == 250.0
    assert updated_service["status"] == "suspended"


def test_json_order_update():
    """测试订单更新操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("order", "update")
    test_data["_id"] = "json_update_test_order"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "order", test_data_json)
    assert err is None

    # 使用JSON参数进行更新
    update_data = load_json_file("order_update_basic.json")
    update_json = json.dumps(update_data)

    result, err = vcloudClient.execute("update", str, "order", update_json)
    assert err is None

    # 验证更新结果
    result, err = vcloudClient.executeReadOnly("get", str, "order", update_data["_id"])
    assert err is None
    updated_order = json.loads(result)
    assert updated_order["amount"] == 750.0
    assert updated_order["status"] == "paid"
    assert updated_order["amountPaid"] == 750.0


# ========== DELETE 操作测试 ==========

def test_json_user_service_delete():
    """测试用户服务删除操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "delete")
    test_data["_id"] = "json_delete_test_service_1"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None

    # 使用JSON参数进行删除
    delete_data = load_json_file("user_service_delete_by_ids.json")
    delete_json = json.dumps(delete_data)

    result, err = vcloudClient.execute("delete", str, "user_service", delete_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1


def test_json_order_delete():
    """测试订单删除操作"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("order", "delete")
    test_data["_id"] = "json_delete_test_order_1"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "order", test_data_json)
    assert err is None

    # 使用JSON参数进行删除
    delete_data = load_json_file("order_delete_by_ids.json")
    delete_json = json.dumps(delete_data)

    result, err = vcloudClient.execute("delete", str, "order", delete_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1


# ========== DELETE_MANY 操作测试 ==========

def test_json_user_service_delete_many():
    """测试用户服务批量删除操作"""
    # 先创建测试数据
    test_services = []
    for i in range(3):
        test_data = create_test_data_for_operation("user_service", "delete_many", str(i))
        test_data["address"] = "0xjson_delete_many_address"
        test_data["status"] = "active"
        test_services.append(test_data)
        test_data_json = json.dumps(test_data)
        result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
        assert err is None

    # 使用JSON参数进行批量删除
    delete_data = load_json_file("user_service_delete_many_by_address_status.json")
    delete_json = json.dumps(delete_data)

    result, err = vcloudClient.execute("delete_many", str, "user_service", delete_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] >= 3


def test_json_order_delete_many():
    """测试订单批量删除操作"""
    # 先创建测试数据
    test_orders = []
    for i in range(3):
        test_data = create_test_data_for_operation("order", "delete_many", str(i))
        test_data["address"] = "0xjson_delete_many_order_address"
        test_data["status"] = "pending"
        test_orders.append(test_data)
        test_data_json = json.dumps(test_data)
        result, err = vcloudClient.execute("insert", str, "order", test_data_json)
        assert err is None

    # 使用JSON参数进行批量删除
    delete_data = load_json_file("order_delete_many_by_address_statuses.json")
    delete_json = json.dumps(delete_data)

    result, err = vcloudClient.execute("delete_many", str, "order", delete_json)
    assert err is None

    delete_result = json.loads(result)
    assert delete_result["deleted"] >= 3


# ========== BULK_WRITE 操作测试 ==========

def test_json_user_service_bulk_write():
    """测试用户服务批量写入操作"""
    bulk_data = load_json_file("user_service_bulk_write.json")
    bulk_json = json.dumps(bulk_data)

    result, err = vcloudClient.execute("bulk_write", str, "user_service", bulk_json)
    # 注意：此测试依赖于bulk_write实现
    # 可能需要根据实际实现进行调整
    print(f"Bulk write result: {result}, error: {err}")


def test_json_order_bulk_write():
    """测试订单批量写入操作"""
    bulk_data = load_json_file("order_bulk_write.json")
    bulk_json = json.dumps(bulk_data)

    result, err = vcloudClient.execute("bulk_write", str, "order", bulk_json)
    # 注意：此测试依赖于bulk_write实现
    # 可能需要根据实际实现进行调整
    print(f"Bulk write result: {result}, error: {err}")


# ========== UPDATE_MANY 操作测试 ==========

def test_json_user_service_update_many():
    """测试用户服务批量更新操作"""
    # 先创建测试数据
    test_services = []
    for i in range(3):
        test_data = create_test_data_for_operation("user_service", "update_many", str(i))
        test_data["address"] = "0xjson_update_many_address"
        test_data["status"] = "active"
        test_services.append(test_data)
        test_data_json = json.dumps(test_data)
        result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
        assert err is None
        assert result == test_data["_id"]

    # 使用JSON参数进行批量更新
    update_data = load_json_file("user_service_update_many.json")
    update_json = json.dumps(update_data)

    result, err = vcloudClient.execute("update_many", str, "user_service", update_json)
    # 注意：此测试依赖于update_many实现
    print(f"Update many result: {result}, error: {err}")


def test_json_order_update_many():
    """测试订单批量更新操作"""
    # 先创建测试数据
    test_orders = []
    for i in range(3):
        test_data = create_test_data_for_operation("order", "update_many", str(i))
        test_data["address"] = "0xjson_update_many_order_address"
        test_data["status"] = "pending"
        test_orders.append(test_data)
        test_data_json = json.dumps(test_data)
        result, err = vcloudClient.execute("insert", str, "order", test_data_json)
        assert err is None
        assert result == test_data["_id"]

    # 使用JSON参数进行批量更新
    update_data = load_json_file("order_update_many.json")
    update_json = json.dumps(update_data)

    result, err = vcloudClient.execute("update_many", str, "order", update_json)
    # 注意：此测试依赖于update_many实现
    print(f"Update many result: {result}, error: {err}")


# ========== 高级查询测试 ==========

def test_json_user_service_find_by_provider():
    """测试按提供商查找用户服务"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "find_provider")
    test_data["provider"] = "json_provider_alpha"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None

    # 使用JSON参数进行查找
    filter_data = load_json_file("user_service_find_by_provider.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == test_data["_id"]:
            assert service["provider"] == "json_provider_alpha"


def test_json_user_service_find_by_service_activated():
    """测试按服务激活状态查找用户服务"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "find_activated")
    test_data["address"] = "0xjson_activated_address"
    test_data["serviceActivated"] = True
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None

    # 使用JSON参数进行查找
    filter_data = load_json_file("user_service_find_by_service_activated.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == test_data["_id"]:
            assert service["serviceActivated"] == True


def test_json_order_find_by_recipient():
    """测试按接收者查找订单"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("order", "find_recipient")
    test_data["recipient"] = "0xjson_recipient_alpha"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "order", test_data_json)
    assert err is None

    # 使用JSON参数进行查找
    filter_data = load_json_file("order_find_by_recipient.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    for order in found_orders:
        if order["_id"] == test_data["_id"]:
            assert order["recipient"] == "0xjson_recipient_alpha"


def test_json_order_find_by_type():
    """测试按类型查找订单"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("order", "find_type")
    test_data["type"] = "compute_order"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "order", test_data_json)
    assert err is None

    # 使用JSON参数进行查找
    filter_data = load_json_file("order_find_by_type.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1
    for order in found_orders:
        if order["_id"] == test_data["_id"]:
            assert order["type"] == "compute_order"


# ========== 分页测试 ==========

def test_json_user_service_pagination():
    """测试用户服务分页查询"""
    # 先创建多个测试数据
    test_services = []
    for i in range(5):
        test_data = create_test_data_for_operation("user_service", "pagination", str(i))
        test_data["address"] = "0xjson_pagination_address"
        test_services.append(test_data)
        test_data_json = json.dumps(test_data)
        result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
        assert err is None
        assert result == test_data["_id"]

    # 使用JSON参数进行分页查询 - 第一页
    filter_data = load_json_file("user_service_find_pagination.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page1_services = json.loads(result)
    assert len(page1_services) <= 2  # limit设置为2

    # 第二页
    filter_data["offset"] = 2
    filter_json = json.dumps(filter_data)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page2_services = json.loads(result)
    assert len(page2_services) <= 2

    # 验证没有重复
    page1_ids = {s["_id"] for s in page1_services}
    page2_ids = {s["_id"] for s in page2_services}
    assert len(page1_ids.intersection(page2_ids)) == 0


def test_json_user_service_complex_query():
    """测试用户服务复杂查询"""
    # 先创建测试数据
    test_data = create_test_data_for_operation("user_service", "complex")
    test_data["address"] = "0xjson_complex_address"
    test_data["status"] = "active"
    test_data["provider"] = "json_complex_provider"
    test_data_json = json.dumps(test_data)
    result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
    assert err is None
    assert result == test_data["_id"]

    # 使用JSON参数进行复杂查询
    filter_data = load_json_file("user_service_find_complex_query.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1
    for service in found_services:
        if service["_id"] == test_data["_id"]:
            assert service["address"] == "0xjson_complex_address"
            assert service["status"] == "active"
            assert service["provider"] == "json_complex_provider"


# ========== 错误处理测试 ==========

def test_json_error_handling_invalid_json():
    """测试无效JSON的错误处理"""
    # 测试无效JSON
    _, err = vcloudClient.executeReadOnly("find", str, "user_service", "invalid_json{")
    assert err is not None

    # 测试空表名
    _, err = vcloudClient.executeReadOnly("find", str, "", "{}")
    assert err is not None

    # 测试不支持的表名
    _, err = vcloudClient.executeReadOnly("find", str, "unsupported_table", "{}")
    assert err is not None
    assert "Unsupported table name" in str(err)


def test_json_get_nonexistent():
    """测试获取不存在的记录"""
    # 测试获取不存在的用户服务
    _, err = vcloudClient.executeReadOnly("get", str, "user_service", "nonexistent_id_12345")
    assert err is not None
    assert "not found" in str(err)

    # 测试获取不存在的订单
    _, err = vcloudClient.executeReadOnly("get", str, "order", "nonexistent_order_id_12345")
    assert err is not None
    assert "not found" in str(err)


# ========== 生命周期测试 ==========

def test_json_complete_lifecycle():
    """测试完整的生命周期操作"""
    # 1. 创建用户服务
    service_data = create_test_data_for_operation("user_service", "lifecycle")
    service_data["_id"] = "json_lifecycle_service"
    service_data["status"] = "pending"
    service_json = json.dumps(service_data)
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    assert result == service_data["_id"]

    # 2. 获取服务
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_data["_id"])
    assert err is None
    retrieved_service = json.loads(result)
    assert retrieved_service["status"] == "pending"

    # 3. 更新服务状态
    retrieved_service["status"] = "active"
    retrieved_service["amount"] = 300.0
    updated_service_json = json.dumps(retrieved_service)
    result, err = vcloudClient.execute("update", str, "user_service", updated_service_json)
    assert err is None

    # 4. 查找服务
    filter_params = {
        "address": service_data["address"],
        "status": "active"
    }
    filter_json = json.dumps(filter_params)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None
    found_services = json.loads(result)
    assert len(found_services) >= 1
    assert any(s["_id"] == service_data["_id"] for s in found_services)

    # 5. 计数服务
    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None
    count = int(result)
    assert count >= 1

    # 6. 删除服务
    delete_filter = {"ids": [service_data["_id"]]}
    delete_filter_json = json.dumps(delete_filter)
    result, err = vcloudClient.execute("delete", str, "user_service", delete_filter_json)
    assert err is None
    delete_result = json.loads(result)
    assert delete_result["deleted"] == 1

    # 7. 验证服务已删除
    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_data["_id"])
    assert err is not None
    assert "not found" in str(err)


# ========== 空结果测试 ==========

def test_json_user_service_find_empty_results():
    """测试查找返回空结果的用户服务"""
    filter_data = load_json_file("user_service_find_empty_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) == 0


def test_json_order_find_empty_results():
    """测试查找返回空结果的订单"""
    filter_data = load_json_file("order_find_empty_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) == 0


def test_json_user_service_count_zero_results():
    """测试计数返回零的用户服务"""
    filter_data = load_json_file("user_service_count_zero_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None

    count = int(result)
    assert count == 0


def test_json_order_count_zero_results():
    """测试计数返回零的订单"""
    filter_data = load_json_file("order_count_zero_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None

    count = int(result)
    assert count == 0


# ========== IDs过滤器测试 ==========

def test_json_user_service_find_by_ids():
    """测试使用IDs过滤器查找用户服务"""
    # 先创建多个测试数据
    test_services = []
    for i in range(3):
        test_data = create_test_data_for_operation("user_service", "ids_filter", str(i))
        test_data["_id"] = f"json_ids_filter_test_{i}"
        test_data["address"] = "0xjson_ids_filter_address"
        test_services.append(test_data)
        test_data_json = json.dumps(test_data)
        result, err = vcloudClient.execute("insert", str, "user_service", test_data_json)
        assert err is None
        assert result == test_data["_id"]

    # 使用JSON参数查找特定IDs
    filter_data = load_json_file("user_service_find_by_ids.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 2
    found_ids = [s["_id"] for s in found_services]
    assert "json_ids_filter_test_0" in found_ids
    assert "json_ids_filter_test_2" in found_ids


def test_json_order_find_by_ids():
    """测试使用IDs过滤器查找订单"""
    # 先创建多个测试数据
    test_orders = []
    for i in range(3):
        test_data = create_test_data_for_operation("order", "ids_filter", str(i))
        test_data["_id"] = f"json_ids_filter_order_test_{i}"
        test_data["address"] = "0xjson_ids_filter_order_address"
        test_orders.append(test_data)
        test_data_json = json.dumps(test_data)
        result, err = vcloudClient.execute("insert", str, "order", test_data_json)
        assert err is None
        assert result == test_data["_id"]

    # 使用JSON参数查找特定IDs
    filter_data = load_json_file("order_find_by_ids.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 2
    found_ids = [o["_id"] for o in found_orders]
    assert "json_ids_filter_order_test_0" in found_ids
    assert "json_ids_filter_order_test_2" in found_ids


# ========== 数据验证测试 ==========

def test_json_data_validation_empty_id():
    """测试空ID的数据验证"""
    # 测试用户服务空ID
    service_data = load_json_file("user_service_insert_basic.json")
    service_data["_id"] = ""
    service_json = json.dumps(service_data)

    _, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is not None
    assert "ID cannot be empty" in str(err)

    # 测试订单空ID
    order_data = load_json_file("order_insert_basic.json")
    order_data["_id"] = ""
    order_json = json.dumps(order_data)

    _, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is not None
    assert "ID cannot be empty" in str(err)


def test_json_duplicate_id_error():
    """测试重复ID错误"""
    # 测试用户服务重复ID
    service_data = load_json_file("user_service_insert_basic.json")
    service_data["_id"] = "json_duplicate_test_service"
    service_json = json.dumps(service_data)

    # 第一次插入应该成功
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is None
    assert result == service_data["_id"]

    # 第二次插入应该失败
    result, err = vcloudClient.execute("insert", str, "user_service", service_json)
    assert err is not None
    assert "already exists" in str(err)

    # 测试订单重复ID
    order_data = load_json_file("order_insert_basic.json")
    order_data["_id"] = "json_duplicate_test_order"
    order_json = json.dumps(order_data)

    # 第一次插入应该成功
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is None
    assert result == order_data["_id"]

    # 第二次插入应该失败
    result, err = vcloudClient.execute("insert", str, "order", order_json)
    assert err is not None
    assert "already exists" in str(err)
