import json
import os
import pytest
from vm import ContractTester

# 初始化合约测试器
vcloudClient = ContractTester(
    wasmName="vcloud_db",
)


@pytest.fixture(autouse=True)
def register_contract():
    """自动注册合约"""
    vcloudClient.constructor()


def load_json_file(filename):
    """加载JSON文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    json_dir = os.path.join(current_dir, "test-vcloud-json-example")
    file_path = os.path.join(json_dir, filename)

    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)


def load_test_data(table_name):
    """加载测试数据文件"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if table_name == "order":
        data_file = os.path.join(current_dir, "test-orders-data.json")
    elif table_name == "user_service":
        data_file = os.path.join(current_dir, "test-userservice-data.json")
    else:
        raise ValueError(f"Unknown table name: {table_name}")

    with open(data_file, 'r', encoding='utf-8') as f:
        return json.load(f)


def setup_test_data():
    """设置测试数据"""
    # 加载并插入订单数据
    orders_data = load_test_data("order")
    for order in orders_data:
        order_json = json.dumps(order)
        vcloudClient.execute("insert", str, "order", order_json)

    # 加载并插入用户服务数据
    user_services_data = load_test_data("user_service")
    for service in user_services_data:
        service_json = json.dumps(service)
        vcloudClient.execute("insert", str, "user_service", service_json)


# ========== 订单相关测试 ==========

def test_json_order_find_by_address_statuses():
    """测试按地址和状态查找订单"""
    setup_test_data()

    filter_data = load_json_file("order_find_by_address_statuses.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1

    # 验证返回的订单符合过滤条件
    for order in found_orders:
        assert order["address"] == "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR"
        assert order["status"] in ["OrderPending"]


def test_json_order_find_by_provider():
    """测试按提供商查找订单"""
    setup_test_data()

    filter_data = load_json_file("order_find_by_provider.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1

    # 验证返回的订单符合过滤条件
    for order in found_orders:
        assert order["provider"] == "v-kube-service"


def test_json_order_find_by_recipient():
    """测试按接收者查找订单"""
    setup_test_data()

    filter_data = load_json_file("order_find_by_recipient.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1

    # 验证返回的订单符合过滤条件
    for order in found_orders:
        assert order["recipient"] == "AUDXBc8xUsEDKM2b29HZSbaFFN5VMv7ijxr"


def test_json_order_find_by_type():
    """测试按类型查找订单"""
    setup_test_data()

    filter_data = load_json_file("order_find_by_type.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 1

    # 验证返回的订单符合过滤条件
    for order in found_orders:
        assert order["type"] == "OrderTypePurchase"


def test_json_order_find_by_ids():
    """测试按IDs查找订单"""
    setup_test_data()

    filter_data = load_json_file("order_find_by_ids.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) >= 2

    # 验证返回的订单ID正确
    found_ids = [order["_id"] for order in found_orders]
    assert "6851359d6e8b76db007df830" in found_ids
    assert "6851349d6e8b76db007df82e" in found_ids


def test_json_order_count_by_address_statuses():
    """测试按地址和状态计数订单"""
    setup_test_data()

    filter_data = load_json_file("order_count_by_address_statuses.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None

    count = int(result)
    assert count >= 1


def test_json_order_count_by_provider():
    """测试按提供商计数订单"""
    setup_test_data()

    filter_data = load_json_file("order_count_by_provider.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None

    count = int(result)
    assert count >= 1


def test_json_order_get_by_id():
    """测试按ID获取订单"""
    setup_test_data()

    # 直接使用字符串ID，因为get操作需要的是字符串而不是JSON对象
    order_id = "6851359d6e8b76db007df830"

    result, err = vcloudClient.executeReadOnly("get", str, "order", order_id)
    assert err is None

    retrieved_order = json.loads(result)
    assert retrieved_order["_id"] == "6851359d6e8b76db007df830"
    assert retrieved_order["address"] == "AUEcxSD6hM2ngNRV5FwB9sHfkSk9wvTprhR"


# ========== 用户服务相关测试 ==========

def test_json_user_service_find_by_address_status():
    """测试按地址和状态查找用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_find_by_address_status.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1

    # 验证返回的服务符合过滤条件
    for service in found_services:
        assert service["address"] == "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS"
        assert service["status"] == "ServiceInactivated"


def test_json_user_service_find_by_provider():
    """测试按提供商查找用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_find_by_provider.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1

    # 验证返回的服务符合过滤条件
    for service in found_services:
        assert service["provider"] == "v-kube-service"


def test_json_user_service_find_by_service_activated():
    """测试按服务激活状态查找用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_find_by_service_activated.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 1

    # 验证返回的服务符合过滤条件
    for service in found_services:
        assert service["address"] == "ATzREX6PWyyPctDs4Rv3S9dg8coEgTHZtYb"
        assert service["serviceActivated"] == True


def test_json_user_service_find_by_ids():
    """测试按IDs查找用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_find_by_ids.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) >= 2

    # 验证返回的服务ID正确
    found_ids = [service["_id"] for service in found_services]
    assert "684108c2a4e5f6b2d4c2dad5" in found_ids
    assert "68410844a4e5f6b2d4c2dad3" in found_ids


def test_json_user_service_count_by_address_status():
    """测试按地址和状态计数用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_count_by_address_status.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None

    count = int(result)
    assert count >= 1


def test_json_user_service_count_by_provider():
    """测试按提供商计数用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_count_by_provider.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None

    count = int(result)
    assert count >= 1


def test_json_user_service_get_by_id():
    """测试按ID获取用户服务"""
    setup_test_data()

    # 直接使用字符串ID，因为get操作需要的是字符串而不是JSON对象
    service_id = "684108c2a4e5f6b2d4c2dad5"

    result, err = vcloudClient.executeReadOnly("get", str, "user_service", service_id)
    assert err is None

    retrieved_service = json.loads(result)
    assert retrieved_service["_id"] == "684108c2a4e5f6b2d4c2dad5"
    assert retrieved_service["address"] == "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS"


# ========== 分页和复杂查询测试 ==========

def test_json_user_service_pagination():
    """测试用户服务分页查询"""
    setup_test_data()

    # 使用JSON参数进行分页查询 - 第一页
    filter_data = load_json_file("user_service_find_pagination.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page1_services = json.loads(result)
    assert len(page1_services) <= 2  # limit设置为2

    # 第二页
    filter_data["offset"] = 2
    filter_json = json.dumps(filter_data)
    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    page2_services = json.loads(result)
    assert len(page2_services) <= 2

    # 验证没有重复
    if len(page1_services) > 0 and len(page2_services) > 0:
        page1_ids = {s["_id"] for s in page1_services}
        page2_ids = {s["_id"] for s in page2_services}
        assert len(page1_ids.intersection(page2_ids)) == 0


def test_json_user_service_complex_query():
    """测试用户服务复杂查询"""
    setup_test_data()

    # 使用JSON参数进行复杂查询
    filter_data = load_json_file("user_service_find_complex_query.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    # 验证返回的服务符合过滤条件
    for service in found_services:
        assert service["address"] == "ATyeRLf9JPHskizMxEzP88V3iEK3gix9yuS"
        assert service["status"] == "ServicePending"
        assert service["provider"] == "v-kube-service"


# ========== 空结果测试 ==========

def test_json_order_find_empty_results():
    """测试查找返回空结果的订单"""
    setup_test_data()

    filter_data = load_json_file("order_find_empty_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "order", filter_json)
    assert err is None

    found_orders = json.loads(result)
    assert len(found_orders) == 0


def test_json_user_service_find_empty_results():
    """测试查找返回空结果的用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_find_empty_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("find", str, "user_service", filter_json)
    assert err is None

    found_services = json.loads(result)
    assert len(found_services) == 0


def test_json_order_count_zero_results():
    """测试计数返回零的订单"""
    setup_test_data()

    filter_data = load_json_file("order_count_zero_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "order", filter_json)
    assert err is None

    count = int(result)
    assert count == 0


def test_json_user_service_count_zero_results():
    """测试计数返回零的用户服务"""
    setup_test_data()

    filter_data = load_json_file("user_service_count_zero_results.json")
    filter_json = json.dumps(filter_data)

    result, err = vcloudClient.executeReadOnly("count", str, "user_service", filter_json)
    assert err is None

    count = int(result)
    assert count == 0


# ========== 错误处理测试 ==========

def test_json_error_handling_invalid_json():
    """测试无效JSON的错误处理"""
    # 测试无效JSON
    _, err = vcloudClient.executeReadOnly("find", str, "user_service", "invalid_json{")
    assert err is not None

    # 测试空表名
    _, err = vcloudClient.executeReadOnly("find", str, "", "{}")
    assert err is not None

    # 测试不支持的表名
    _, err = vcloudClient.executeReadOnly("find", str, "unsupported_table", "{}")
    assert err is not None


def test_json_get_nonexistent():
    """测试获取不存在的记录"""
    setup_test_data()

    # 测试获取不存在的用户服务
    _, err = vcloudClient.executeReadOnly("get", str, "user_service", "nonexistent_id_12345")
    assert err is not None

    # 测试获取不存在的订单
    _, err = vcloudClient.executeReadOnly("get", str, "order", "nonexistent_order_id_12345")
    assert err is not None



